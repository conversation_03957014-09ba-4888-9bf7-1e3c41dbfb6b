import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { razorpayService } from '@/lib/razorpay-service'
import { z } from 'zod'

const createOrderSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required'),
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().optional().default('INR')
})

// POST /api/payments/create-order - Create Razorpay order for course payment
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: createOrderSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { courseId, amount, currency } = validatedBody

      // Verify course exists and is available for purchase
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          instructor: {
            select: { id: true, name: true }
          }
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      if (!course.isActive || !course.isPublished) {
        return APIResponse.error('Course is not available for purchase', 400)
      }

      if (course.price === 0) {
        return APIResponse.error('This course is free. No payment required.', 400)
      }

      // Verify the amount matches the course price
      if (Math.abs(amount - course.price) > 0.01) {
        return APIResponse.error('Payment amount does not match course price', 400)
      }

      // Check if user is already enrolled
      const existingEnrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: courseId
          }
        }
      })

      if (existingEnrollment && existingEnrollment.status === 'active') {
        return APIResponse.error('You are already enrolled in this course', 400)
      }

      // Check for existing pending payment
      const existingPayment = await prisma.payment.findFirst({
        where: {
          userId: user.id,
          courseId: courseId,
          status: 'created'
        },
        orderBy: { createdAt: 'desc' }
      })

      // If there's a recent pending payment (within 30 minutes), return it
      if (existingPayment) {
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)
        if (existingPayment.createdAt > thirtyMinutesAgo) {
          return APIResponse.success({
            orderId: existingPayment.razorpayOrderId,
            amount: Math.round(existingPayment.amount * 100), // Convert to paise
            currency: existingPayment.currency,
            keyId: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
            course: {
              id: course.id,
              title: course.title,
              price: course.price,
              instructor: course.instructor
            },
            user: {
              name: user.name,
              email: user.email
            }
          })
        }
      }

      // Create new Razorpay order
      const orderData = await razorpayService.createOrder({
        amount,
        currency,
        courseId,
        userId: user.id,
        courseName: course.title,
        userEmail: user.email || '',
        userName: user.name || 'Student'
      })

      return APIResponse.success({
        ...orderData,
        course: {
          id: course.id,
          title: course.title,
          price: course.price,
          instructor: course.instructor
        },
        user: {
          name: user.name,
          email: user.email
        }
      })

    } catch (error) {
      console.error('Error creating payment order:', error)
      return APIResponse.error('Failed to create payment order', 500)
    }
  }
)
