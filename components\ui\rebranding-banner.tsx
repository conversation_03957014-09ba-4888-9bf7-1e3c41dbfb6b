'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Spark<PERSON>, ArrowRight } from 'lucide-react';

export function RebrandingBanner() {
  const [hasAnimated, setHasAnimated] = useState(false);

  useEffect(() => {
    setHasAnimated(true);
  }, []);

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -100, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -100, scale: 0.95 }}
        transition={{ 
          duration: 0.8, 
          ease: [0.25, 0.46, 0.45, 0.94],
          delay: hasAnimated ? 0 : 1
        }}
        className="fixed top-4 left-4 z-[9999] bg-gradient-to-r from-violet-600 via-purple-600 to-blue-600 backdrop-blur-md border-2 border-white/30 dark:border-white/20 rounded-xl shadow-2xl max-w-xs sm:max-w-sm pointer-events-auto ring-1 ring-black/5 dark:ring-white/10"
      >
        {/* Animated Background Pattern */}
        <div className="absolute inset-0 overflow-hidden rounded-xl">
          <div className="absolute inset-0 bg-gradient-to-r from-violet-600/90 via-purple-600/90 to-blue-600/90 rounded-xl" />
          <motion.div
            animate={{
              backgroundPosition: ['0% 0%', '100% 100%'],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              repeatType: 'reverse',
            }}
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent bg-[length:200%_200%]"
          />
          
          {/* Floating Sparkles */}
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute"
              style={{
                left: `${15 + i * 15}%`,
                top: `${20 + (i % 2) * 40}%`,
              }}
              animate={{
                y: [0, -10, 0],
                opacity: [0.3, 1, 0.3],
                scale: [0.8, 1.2, 0.8],
              }}
              transition={{
                duration: 3 + i * 0.5,
                repeat: Infinity,
                delay: i * 0.3,
              }}
            >
              <Sparkles className="w-4 h-4 text-white/60" />
            </motion.div>
          ))}
        </div>

        <div className="relative p-3 sm:p-4">
          <div className="flex items-start space-x-2 sm:space-x-3">
            {/* Icon */}
            <motion.div
              initial={{ rotate: 0, scale: 1 }}
              animate={{ rotate: [0, 10, -10, 0], scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              className="flex-shrink-0 mt-0.5"
            >
              <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-white/20 dark:bg-white/30 backdrop-blur-sm flex items-center justify-center">
                <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
              </div>
            </motion.div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
                className="flex items-center space-x-1 mb-1"
              >
                <span className="text-white/90 dark:text-white/95 text-xs font-medium">
                  🎉 Exciting News!
                </span>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5, duration: 0.6 }}
                className="flex flex-col space-y-1"
              >
                <div className="flex items-center">
                  <span className="text-white dark:text-white font-semibold text-xs sm:text-sm">
                    NextGen Classes is becoming
                  </span>
                </div>
                <div className="flex items-center space-x-1 sm:space-x-2">
                  <motion.span
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.8, duration: 0.6 }}
                    className="text-white dark:text-white font-bold text-sm sm:text-base bg-white/20 dark:bg-white/30 px-2 py-0.5 sm:py-1 rounded-md backdrop-blur-sm"
                  >
                    PrepLocus
                  </motion.span>
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1, duration: 0.6 }}
                  >
                    <ArrowRight className="w-3 h-3 text-white/80 dark:text-white/90" />
                  </motion.div>
                </div>
              </motion.div>

             
            </div>
          </div>
        </div>

        {/* Bottom Glow Effect */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/30 dark:via-white/40 to-transparent rounded-b-xl" />
      </motion.div>
    </AnimatePresence>
  );
}
