const { createServer } = require('http')
const { Server } = require('socket.io')
const next = require('next')
const { PrismaClient } = require('../lib/generated/prisma')

const prisma = new PrismaClient()

const dev = process.env.NODE_ENV !== 'production'
const hostname = 'localhost'
const port = process.env.SOCKET_PORT || 3001

// Create Next.js app
const app = next({ dev, hostname, port: 3000 })
const handler = app.getRequestHandler()

// Store connected users
const connectedUsers = new Map()
const activeRooms = new Map()

app.prepare().then(() => {
  const httpServer = createServer((req, res) => {
    handler(req, res)
  })

  const io = new Server(httpServer, {
    cors: {
      origin: (origin, callback) => {
        const allowed = [process.env.NEXTAUTH_URL, process.env.AUTH_URL, 'http://localhost:3000'].filter(Boolean)
        if (!origin || allowed.includes(origin)) return callback(null, true)
        return callback(new Error('CORS not allowed'))
      },
      methods: ["GET", "POST"],
      credentials: true
    },
    path: '/socket.io',
    transports: ['websocket', 'polling']
  })

  // Socket connection handling
  io.on('connection', (socket) => {
    console.log('🔌 New socket connection:', socket.id)

    // Expect handshake token and verify; reject if invalid
    const token = socket.handshake.auth && socket.handshake.auth.token
    const verify = (tkn, secret) => {
      try {
        const [h, p, s] = tkn.split('.')
        if (!h || !p || !s) return null
        const data = `${h}.${p}`
        const expected = require('crypto').createHmac('sha256', secret).update(data).digest()
        const provided = Buffer.from(s.replace(/-/g, '+').replace(/_/g, '/'), 'base64')
        if (!require('crypto').timingSafeEqual(expected, provided)) return null
        const payload = JSON.parse(Buffer.from(p.replace(/-/g, '+').replace(/_/g, '/'), 'base64').toString('utf8'))
        const now = Math.floor(Date.now() / 1000)
        if (payload.exp && now > payload.exp) return null
        return payload
      } catch { return null }
    }
    const secret = process.env.AUTH_SECRET || process.env.NEXTAUTH_SECRET || ''
    const payload = token ? verify(token, secret) : null
    if (!payload || payload.purpose !== 'socket-auth' || !payload.sub) {
      socket.emit('authentication_error', { message: 'Authentication required' })
      socket.disconnect()
      return
    }

    // Register connected user using verified payload
    connectedUsers.set(socket.id, {
      userId: payload.sub,
      role: payload.role || 'STUDENT',
      name: payload.name || 'Unknown'
    })
    socket.join(`user:${payload.sub}`)
    if (payload.role) socket.join(`role:${payload.role}`)

    // Handle notifications
    socket.on('send_notification', (data) => {
      const user = connectedUsers.get(socket.id)
      if (!user || user.role !== 'ADMIN') {
        socket.emit('error', { message: 'Unauthorized' })
        return
      }

      const notification = {
        id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: data.type,
        title: data.title,
        message: data.message,
        data: data.data || {},
        createdAt: new Date(),
        senderId: user.userId,
        senderName: user.name
      }

      if (data.broadcast) {
        io.emit('notification', notification)
        console.log(`📢 Notification broadcasted: ${data.title}`)
      } else if (data.targetUserId) {
        io.to(`user:${data.targetUserId}`).emit('notification', notification)
        console.log(`📨 Notification sent to ${data.targetUserId}: ${data.title}`)
      }

      socket.emit('notification_sent', {
        success: true,
        notification,
        target: data.broadcast ? 'all' : data.targetUserId
      })
    })

    // Handle quiz progress
    socket.on('quiz_progress', (data) => {
      const user = connectedUsers.get(socket.id)
      if (!user) return

      const progressUpdate = {
        ...data,
        userId: user.userId,
        userName: user.name,
        timestamp: new Date()
      }

      socket.to(`quiz:${data.quizId}`).emit('quiz_progress_update', progressUpdate)
      socket.to('role:ADMIN').emit('quiz_progress_update', progressUpdate)
    })

    // Handle quiz room management
    socket.on('join_quiz', (quizId) => {
      socket.join(`quiz:${quizId}`)
      
      if (!activeRooms.has(`quiz:${quizId}`)) {
        activeRooms.set(`quiz:${quizId}`, new Set())
      }
      activeRooms.get(`quiz:${quizId}`).add(socket.id)

      console.log(`🎯 User joined quiz room: ${quizId}`)
    })

    socket.on('leave_quiz', (quizId) => {
      socket.leave(`quiz:${quizId}`)
      
      if (activeRooms.has(`quiz:${quizId}`)) {
        activeRooms.get(`quiz:${quizId}`).delete(socket.id)
        if (activeRooms.get(`quiz:${quizId}`).size === 0) {
          activeRooms.delete(`quiz:${quizId}`)
        }
      }

      console.log(`🚪 User left quiz room: ${quizId}`)
    })

    // Handle chat room joining
    socket.on('chat:join', (data) => {
      console.log(`👥 User ${socket.id} joining chat room: ${data.roomId}`)
      socket.join(`chat:${data.roomId}`)

      socket.to(`chat:${data.roomId}`).emit('chat:user_joined', {
        userId: connectedUsers.get(socket.id)?.userId,
        name: connectedUsers.get(socket.id)?.name
      })
    })

    // Handle chat room leaving
    socket.on('chat:leave', (data) => {
      console.log(`👋 User ${socket.id} leaving chat room: ${data.roomId}`)
      socket.leave(`chat:${data.roomId}`)

      socket.to(`chat:${data.roomId}`).emit('chat:user_left', {
        userId: connectedUsers.get(socket.id)?.userId,
        name: connectedUsers.get(socket.id)?.name
      })
    })

    // Handle chat messages
    socket.on('chat:message', async (data) => {
      const user = connectedUsers.get(socket.id)
      if (!user) return

      console.log(`💬 Message from ${user.name} in room ${data.roomId}: ${data.message}`)

      try {
        // Save message to database using Prisma
        const savedMessage = await prisma.chatMessage.create({
          data: {
            roomId: data.roomId,
            userId: user.userId,
            message: data.message,
            type: data.type || 'text'
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
                role: true
              }
            }
          }
        })

        const chatMessage = {
          id: savedMessage.id,
          userId: savedMessage.userId,
          userName: savedMessage.user.name || 'Unknown',
          message: savedMessage.message,
          type: savedMessage.type,
          timestamp: savedMessage.createdAt,
          roomId: savedMessage.roomId
        }

        console.log('✅ Message saved to database:', savedMessage.id)
        io.to(`chat:${data.roomId}`).emit('chat:message_received', chatMessage)
      } catch (error) {
        console.error('❌ Error saving chat message:', error)
        // Still broadcast the message even if saving fails
        const chatMessage = {
          id: `msg_${Date.now()}_${Math.random().toString(36).substring(7)}`,
          userId: user.userId,
          userName: user.name,
          message: data.message,
          type: data.type || 'text',
          timestamp: new Date(),
          roomId: data.roomId
        }
        io.to(`chat:${data.roomId}`).emit('chat:message_received', chatMessage)
      }
    })

    // Handle metrics requests
    socket.on('get_metrics', () => {
      const user = connectedUsers.get(socket.id)
      if (!user || user.role !== 'ADMIN') {
        socket.emit('error', { message: 'Unauthorized' })
        return
      }

      const metrics = {
        connectedUsers: connectedUsers.size,
        activeRooms: activeRooms.size,
        usersByRole: {
          ADMIN: Array.from(connectedUsers.values()).filter(u => u.role === 'ADMIN').length,
          STUDENT: Array.from(connectedUsers.values()).filter(u => u.role === 'STUDENT').length
        },
        timestamp: new Date()
      }

      socket.emit('metrics_update', metrics)
    })

    // Handle disconnection
    socket.on('disconnect', () => {
      const user = connectedUsers.get(socket.id)
      if (user) {
        // Clean up rooms
        activeRooms.forEach((users, roomId) => {
          users.delete(socket.id)
          if (users.size === 0) {
            activeRooms.delete(roomId)
          }
        })

        // Notify admins
        socket.to('role:ADMIN').emit('user:left', {
          userId: user.userId,
          name: user.name,
          role: user.role,
          leftAt: new Date()
        })

        connectedUsers.delete(socket.id)
        console.log(`👋 User disconnected: ${user.name}`)
      }
    })

    // Send connection confirmation
    socket.emit('connected', {
      socketId: socket.id,
      timestamp: new Date()
    })
  })

  // Start server
  httpServer.listen(port, () => {
    console.log(`🚀 Socket.IO server running on http://${hostname}:${port}`)
    console.log(`📡 Socket endpoint: ws://${hostname}:${port}/socket.io`)
  })

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('🛑 SIGTERM received, shutting down gracefully')
    httpServer.close(() => {
      console.log('✅ Socket server closed')
      process.exit(0)
    })
  })
})
