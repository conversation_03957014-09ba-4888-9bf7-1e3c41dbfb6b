'use client'

import { useState, useEffect, useCallback } from 'react'
import { getSocketClient } from '@/lib/socket-client'
import { toast } from '@/lib/toast-utils'

interface MissionProgress {
  id: string
  isStarted: boolean
  isCompleted: boolean
  completionRate: number
  pointsEarned: number
  startedAt?: string
  completedAt?: string
}

interface MissionProgressUpdate {
  missionId: string
  progress: MissionProgress
  missionCompleted: boolean
  achievementsUnlocked: string[]
  nextMissions: string[]
}

interface UseMissionProgressProps {
  courseId: string
  userId: string
  onMissionCompleted?: (missionId: string, pointsEarned: number) => void
  onAchievementUnlocked?: (achievementId: string) => void
}

export function useMissionProgress({
  courseId,
  userId,
  onMissionCompleted,
  onAchievementUnlocked
}: UseMissionProgressProps) {
  const [missionProgress, setMissionProgress] = useState<Map<string, MissionProgress>>(new Map())
  const [loading, setLoading] = useState(false)
  const socket = getSocketClient()

  // Handle mission progress updates from Socket.io
  useEffect(() => {
    // TODO: Implement socket event handlers when public methods are available in SocketClient
    // Currently disabled due to private method access restrictions
    console.log('Mission progress socket handlers disabled - awaiting public API')
  }, [socket, userId, onMissionCompleted, onAchievementUnlocked])

  // Update mission progress
  const updateMissionProgress = useCallback((
    missionId: string,
    contentId: string,
    contentType: 'LESSON' | 'QUIZ' | 'ASSIGNMENT' | 'DISCUSSION',
    isCompleted: boolean
  ) => {
    if (!socket) {
      console.warn('Socket not connected, cannot update mission progress')
      return
    }

    socket.updateMissionProgress({
      missionId,
      contentId,
      contentType,
      isCompleted,
      courseId
    })
  }, [socket, courseId])

  // Fetch initial mission progress
  const fetchMissionProgress = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/student/courses/mission-progress?courseId=${courseId}`)
      
      if (response.ok) {
        const data = await response.json()
        const progressMap = new Map<string, MissionProgress>()
        
        data.data.forEach((progress: any) => {
          progressMap.set(progress.missionId, {
            id: progress.id,
            isStarted: progress.isStarted,
            isCompleted: progress.isCompleted,
            completionRate: progress.completionRate,
            pointsEarned: progress.pointsEarned,
            startedAt: progress.startedAt,
            completedAt: progress.completedAt
          })
        })
        
        setMissionProgress(progressMap)
      }
    } catch (error) {
      console.error('Error fetching mission progress:', error)
    } finally {
      setLoading(false)
    }
  }, [courseId])

  // Get progress for a specific mission
  const getMissionProgress = useCallback((missionId: string): MissionProgress | null => {
    return missionProgress.get(missionId) || null
  }, [missionProgress])

  // Check if mission is unlocked (prerequisites met)
  const isMissionUnlocked = useCallback((
    missionId: string, 
    prerequisites: string[]
  ): boolean => {
    if (prerequisites.length === 0) return true
    
    return prerequisites.every(prereqId => {
      const prereqProgress = missionProgress.get(prereqId)
      return prereqProgress?.isCompleted || false
    })
  }, [missionProgress])

  // Get mission state
  const getMissionState = useCallback((
    missionId: string,
    prerequisites: string[] = []
  ): 'locked' | 'available' | 'in-progress' | 'completed' => {
    const progress = missionProgress.get(missionId)
    
    if (progress?.isCompleted) return 'completed'
    if (progress?.isStarted) return 'in-progress'
    
    const unlocked = isMissionUnlocked(missionId, prerequisites)
    return unlocked ? 'available' : 'locked'
  }, [missionProgress, isMissionUnlocked])

  // Calculate total stats
  const getTotalStats = useCallback(() => {
    const progressArray = Array.from(missionProgress.values())
    
    return {
      totalMissions: progressArray.length,
      completedMissions: progressArray.filter(p => p.isCompleted).length,
      totalPoints: progressArray.reduce((sum, p) => sum + p.pointsEarned, 0),
      averageCompletion: progressArray.length > 0 
        ? progressArray.reduce((sum, p) => sum + p.completionRate, 0) / progressArray.length 
        : 0
    }
  }, [missionProgress])

  return {
    missionProgress: Array.from(missionProgress.values()),
    loading,
    updateMissionProgress,
    fetchMissionProgress,
    getMissionProgress,
    isMissionUnlocked,
    getMissionState,
    getTotalStats
  }
}

// Hook for tracking lesson progress and automatically updating missions
export function useLessonProgressWithMissions({
  courseId,
  userId,
  onMissionCompleted,
  onAchievementUnlocked
}: UseMissionProgressProps) {
  const missionHook = useMissionProgress({
    courseId,
    userId,
    onMissionCompleted,
    onAchievementUnlocked
  })

  // Track lesson completion and update missions
  const trackLessonCompletion = useCallback(async (
    lessonId: string,
    isCompleted: boolean,
    missionId?: string
  ) => {
    try {
      // Update regular lesson progress
      const response = await fetch('/api/student/courses/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lessonId,
          isCompleted
        })
      })

      if (response.ok && missionId) {
        // Update mission progress
        missionHook.updateMissionProgress(missionId, lessonId, 'LESSON', isCompleted)
      }
    } catch (error) {
      console.error('Error tracking lesson completion:', error)
    }
  }, [missionHook])

  // Track quiz completion and update missions
  const trackQuizCompletion = useCallback(async (
    quizId: string,
    isCompleted: boolean,
    missionId?: string
  ) => {
    if (missionId && isCompleted) {
      missionHook.updateMissionProgress(missionId, quizId, 'QUIZ', isCompleted)
    }
  }, [missionHook])

  return {
    ...missionHook,
    trackLessonCompletion,
    trackQuizCompletion
  }
}
