import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return APIResponse.error('Please sign in to view your schedule', 401)
    }

    const { searchParams } = new URL(request.url)
    const filter = searchParams.get('filter') || 'all'
    const timeFilter = searchParams.get('timeFilter') || 'week'

    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      return APIResponse.error('User not found', 404)
    }

    // Calculate date filter based on timeFilter
    let dateFilter = {}
    const now = new Date()
    
    if (timeFilter === 'week') {
      const weekStart = new Date(now)
      weekStart.setDate(now.getDate() - now.getDay())
      weekStart.setHours(0, 0, 0, 0)
      
      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekStart.getDate() + 7)
      
      dateFilter = {
        startTime: {
          gte: weekStart,
          lt: weekEnd
        }
      }
    } else if (timeFilter === 'month') {
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1)
      
      dateFilter = {
        startTime: {
          gte: monthStart,
          lt: monthEnd
        }
      }
    }

    // Get both regular scheduled quizzes and ScheduledQuiz entries
    const [regularScheduledQuizzes, scheduledQuizEntries] = await Promise.all([
      // Regular quizzes with startTime and endTime
      prisma.quiz.findMany({
        where: {
          isPublished: true,
          startTime: { not: null },
          endTime: { not: null },
          ...dateFilter,
          enrollments: {
            some: {
              userId: user.id
            }
          }
        },
        include: {
          creator: {
            select: {
              name: true,
              email: true
            }
          },
          questions: {
            select: {
              id: true
            }
          },
          attempts: {
            where: {
              userId: user.id
            },
            select: {
              id: true,
              score: true,
              percentage: true,
              completedAt: true
            },
            orderBy: {
              completedAt: 'desc'
            }
          },
          enrollments: {
            where: {
              userId: user.id
            },
            select: {
              id: true
            }
          },
          _count: {
            select: {
              enrollments: true
            }
          }
        },
        orderBy: {
          startTime: 'asc'
        }
      }),
      // ScheduledQuiz entries
      prisma.scheduledQuiz.findMany({
        where: {
          isActive: true,
          ...dateFilter,
          enrollments: {
            some: {
              userId: user.id
            }
          }
        },
        include: {
          quiz: {
            include: {
              creator: {
                select: {
                  name: true,
                  email: true
                }
              },
              questions: {
                select: {
                  id: true
                }
              },
              attempts: {
                where: {
                  userId: user.id
                },
                select: {
                  id: true,
                  score: true,
                  percentage: true,
                  completedAt: true
                },
                orderBy: {
                  completedAt: 'desc'
                }
              },
              _count: {
                select: {
                  enrollments: true
                }
              }
            }
          },
          enrollments: {
            where: {
              userId: user.id
            },
            select: {
              id: true
            }
          },
          creator: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: {
          startTime: 'asc'
        }
      })
    ])

    // Transform regular scheduled quizzes
    const transformedRegularQuizzes = regularScheduledQuizzes.map(quiz => {
      const now = new Date()
      const startTime = new Date(quiz.startTime!)
      const endTime = new Date(quiz.endTime!)

      let status: 'upcoming' | 'active' | 'completed' | 'missed'

      if (now < startTime) {
        status = 'upcoming'
      } else if (now >= startTime && now <= endTime) {
        status = 'active'
      } else if (quiz.attempts.length > 0) {
        status = 'completed'
      } else {
        status = 'missed'
      }

      return {
        id: quiz.id,
        title: quiz.title,
        description: quiz.description || '',
        type: quiz.type,
        difficulty: quiz.difficulty,
        startTime: quiz.startTime!.toISOString(),
        endTime: quiz.endTime!.toISOString(),
        duration: quiz.timeLimit || 60,
        questionCount: quiz.questions.length,
        isEnrolled: quiz.enrollments.length > 0,
        status,
        instructor: {
          name: quiz.creator.name || 'Unknown',
          email: quiz.creator.email || ''
        },
        enrollmentCount: quiz._count.enrollments,
        maxAttempts: quiz.maxAttempts,
        userAttempts: quiz.attempts.length
      }
    })

    // Transform ScheduledQuiz entries
    const transformedScheduledQuizzes = scheduledQuizEntries.map(scheduledQuiz => {
      const now = new Date()
      const startTime = new Date(scheduledQuiz.startTime)
      const endTime = new Date(scheduledQuiz.endTime)

      let status: 'upcoming' | 'active' | 'completed' | 'missed'

      if (now < startTime) {
        status = 'upcoming'
      } else if (now >= startTime && now <= endTime) {
        status = 'active'
      } else if (scheduledQuiz.quiz.attempts.length > 0) {
        status = 'completed'
      } else {
        status = 'missed'
      }

      return {
        id: scheduledQuiz.quiz.id,
        title: scheduledQuiz.title, // Use custom title from ScheduledQuiz
        description: scheduledQuiz.description || scheduledQuiz.quiz.description || '',
        type: scheduledQuiz.quiz.type,
        difficulty: scheduledQuiz.quiz.difficulty,
        startTime: scheduledQuiz.startTime.toISOString(),
        endTime: scheduledQuiz.endTime.toISOString(),
        duration: scheduledQuiz.duration || scheduledQuiz.quiz.timeLimit || 60,
        questionCount: scheduledQuiz.quiz.questions.length,
        isEnrolled: scheduledQuiz.enrollments.length > 0,
        status,
        instructor: {
          name: scheduledQuiz.quiz.creator.name || 'Unknown',
          email: scheduledQuiz.quiz.creator.email || ''
        },
        enrollmentCount: scheduledQuiz.quiz._count.enrollments,
        maxAttempts: scheduledQuiz.maxAttempts,
        userAttempts: scheduledQuiz.quiz.attempts.length
      }
    })

    // Combine both types and sort by start time
    const allTransformedQuizzes = [...transformedRegularQuizzes, ...transformedScheduledQuizzes]
      .sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())

    // Filter by status if specified
    const filteredQuizzes = filter === 'all'
      ? allTransformedQuizzes
      : allTransformedQuizzes.filter(quiz => quiz.status === filter)

    return APIResponse.success(
      filteredQuizzes,
      'Schedule retrieved successfully'
    )

  } catch (error) {
    console.error('Error fetching schedule:', error)
    return APIResponse.error('Failed to fetch schedule', 500)
  }
}
