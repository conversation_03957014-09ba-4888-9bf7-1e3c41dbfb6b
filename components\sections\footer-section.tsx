'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  Mail,
  Phone,
  MapPin,
  Smartphone
} from 'lucide-react';
import { useSystemSettings } from '@/hooks/use-system-settings';

const quickLinks = [
  { label: 'About Us', href: '/about' },
  { label: 'Courses', href: '/student/courses' },
  { label: 'Help Center', href: '/help' },
  { label: 'Contact', href: '/contact' },
  { label: 'Sitemap', href: '/sitemap' },
];

const supportLinks = [
  { label: 'Help Center', href: '/help' },
  { label: 'Privacy Policy', href: '/privacy' },
  { label: 'Terms of Service', href: '/terms' },
  { label: 'Refund Policy', href: '/refund' },
  { label: 'Student Support', href: '/contact' },
  { label: 'Technical Support', href: '/contact' },
];

export function FooterSection() {
  const { settings } = useSystemSettings();
  const [categories, setCategories] = useState<any[]>([]);

  // Fetch categories for footer
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/public/categories');
        if (response.ok) {
          const result = await response.json();
          const data = result.data || result;
          if (data.categories && data.categories.length > 0) {
            // Take first 6 categories for footer
            setCategories(data.categories.slice(0, 6));
          }
        }
      } catch (error) {
        console.error('Error fetching categories for footer:', error);
        // Set fallback categories on error
        const fallbackCategories = [
          { name: 'Engineering', id: 'engineering' },
          { name: 'Medical', id: 'medical' },
          { name: 'Government', id: 'government' },
          { name: 'Teaching', id: 'teaching' },
          { name: 'Business', id: 'business' },
          { name: 'Technology', id: 'technology' },
        ];
        setCategories(fallbackCategories);
      }
    };

    fetchCategories();
  }, []);

  const socialLinks = [
    { icon: Facebook, href: settings.facebookPage || '#', label: 'Facebook', color: 'hover:text-blue-600' },
    { icon: Twitter, href: settings.twitterHandle || '#', label: 'Twitter', color: 'hover:text-sky-500' },
    { icon: Instagram, href: settings.instagramHandle || '#', label: 'Instagram', color: 'hover:text-pink-600' },
    { icon: Youtube, href: settings.youtubeChannel || '#', label: 'YouTube', color: 'hover:text-red-600' },
  ].filter(link => link.href !== '#'); // Only show links that are configured

  return (
    <footer className="bg-gradient-to-b from-gray-900 to-black text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-72 h-72 bg-violet-500 rounded-full mix-blend-multiply filter blur-3xl animate-float" />
        <div className="absolute bottom-20 right-20 w-72 h-72 bg-cyan-500 rounded-full mix-blend-multiply filter blur-3xl animate-float-delayed" />
      </div>

      <div className="relative z-10">
        {/* Main Footer Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="lg:col-span-1"
            >
              <h3 className="text-2xl font-bold mb-4 text-gradient bg-gradient-primary">
                {settings.companyName}
              </h3>
              <p className="text-gray-400 mb-6 leading-relaxed">
                {settings.companyDescription}
              </p>

              {/* Contact Info */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-3 text-gray-400">
                  <Mail className="w-5 h-5 text-violet-400" />
                  <span>{settings.contactEmail}</span>
                </div>
                <div className="flex items-center gap-3 text-gray-400">
                  <Phone className="w-5 h-5 text-violet-400" />
                  <span>{settings.contactPhone}</span>
                </div>
                <div className="flex items-center gap-3 text-gray-400">
                  <MapPin className="w-5 h-5 text-violet-400" />
                  <span>{settings.contactAddress}</span>
                </div>
              </div>

              {/* Social Links */}
              <div className="flex gap-4">
                {socialLinks.map((social) => (
                  <motion.a
                    key={social.label}
                    href={social.href}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className={`w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 transition-colors duration-300 ${social.color}`}
                  >
                    <social.icon className="w-5 h-5" />
                  </motion.a>
                ))}
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.label}>
                    <a
                      href={link.href}
                      className="text-gray-400 hover:text-violet-400 transition-colors duration-300"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Exam Categories */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold mb-6">Course Categories</h4>
              <ul className="space-y-3">
                {categories.length > 0 ? (
                  categories.map((category) => (
                    <li key={category.id || category.name}>
                      <a
                        href={`/student/courses?category=${category.id || category.name.toLowerCase()}`}
                        className="text-gray-400 hover:text-violet-400 transition-colors duration-300"
                      >
                        {category.name}
                        {category.courseCount && (
                          <span className="ml-1 text-xs opacity-75">
                            ({category.courseCount})
                          </span>
                        )}
                      </a>
                    </li>
                  ))
                ) : (
                  // Loading skeleton
                  Array.from({ length: 6 }).map((_, index) => (
                    <li key={`category-skeleton-${index}`}>
                      <div className="h-4 bg-gray-700 animate-pulse rounded w-24" />
                    </li>
                  ))
                )}
              </ul>
            </motion.div>

            {/* Support & App Download */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold mb-6">Support</h4>
              <ul className="space-y-3 mb-8">
                {supportLinks.slice(0, 4).map((link) => (
                  <li key={link.label}>
                    <a
                      href={link.href}
                      className="text-gray-400 hover:text-violet-400 transition-colors duration-300"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>

              {/* App Download */}
              <div>
                <h5 className="text-sm font-semibold mb-4 text-gray-300">Download Our App</h5>
                <div className="space-y-3">
                  <motion.a
                    href="#"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors duration-300"
                  >
                    <Smartphone className="w-6 h-6 text-violet-400" />
                    <div>
                      <div className="text-sm font-medium">Download for</div>
                      <div className="text-xs text-gray-400">iOS & Android</div>
                    </div>
                  </motion.a>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="flex flex-col sm:flex-row justify-between items-center gap-4"
            >
              <p className="text-gray-400 text-sm">
                © 2025 {settings.companyName}. All rights reserved. Made with ❤️ for Indian students.
              </p>
              <div className="flex gap-6 text-sm text-gray-400">
                <a href="/privacy" className="hover:text-violet-400 transition-colors">Privacy</a>
                <a href="/terms" className="hover:text-violet-400 transition-colors">Terms</a>
                <a href="/refund" className="hover:text-violet-400 transition-colors">Refund</a>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  );
}
