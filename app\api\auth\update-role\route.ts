import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateRoleSchema = z.object({
  role: z.enum(['STUDENT', 'ADMIN'])
})

export async function POST(request: NextRequest) {
  try {
    // Basic CSRF protection via Origin check for same-site requests
    const origin = request.headers.get('origin') || ''
    const allowedOrigins = [
      process.env.NEXTAUTH_URL || '',
      (process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : ''),
      process.env.APP_URL || '',
      'http://localhost:3000',
      'http://127.0.0.1:3000'
    ].filter(Boolean)

    if (process.env.NODE_ENV !== 'development' && origin && !allowedOrigins.includes(origin)) {
      return NextResponse.json({ error: 'Invalid origin' }, { status: 403 })
    }

    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { role } = updateRoleSchema.parse(body)

    // Only allow elevating to ADMIN if the requester is already an ADMIN or is allowlisted
    const isRequesterAdmin = session.user.role === 'ADMIN'
    const isAllowlistedEmail = Boolean(
      session.user?.email &&
      (process.env.ADMIN_EMAIL_ALLOWLIST || '')
        .split(',')
        .map((e) => e.trim().toLowerCase())
        .filter(Boolean)
        .includes(session.user.email.toLowerCase())
    )

    if (role === 'ADMIN' && !(isRequesterAdmin || isAllowlistedEmail)) {
      return NextResponse.json(
        { error: 'Forbidden: cannot escalate privileges' },
        { status: 403 }
      )
    }

    // Update user role in database
    const updated = await prisma.user.update({
      where: { id: session.user.id },
      data: { role }
    })

    return NextResponse.json({ success: true, role: updated.role })
  } catch (error) {
    console.error('Error updating user role:', error)
    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    )
  }
}
