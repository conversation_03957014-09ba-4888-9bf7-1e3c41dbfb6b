import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// POST /api/admin/live-quiz/sessions/[id]/stop - Stop live quiz session
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Get session with participants
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              questions: {
                select: { id: true },
                orderBy: { order: 'asc' }
              }
            }
          },
          participants: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              }
            },
            orderBy: [
              { score: 'desc' },
              { totalAnswered: 'desc' },
              { timeSpent: 'asc' }
            ]
          }
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Check if session can be stopped
      if (session.status === 'COMPLETED') {
        return APIResponse.error('Session is already completed', 400, 'SESSION_ALREADY_COMPLETED')
      }

      if (session.status === 'WAITING') {
        return APIResponse.error('Cannot stop session that hasn\'t started', 400, 'SESSION_NOT_STARTED')
      }

      // Calculate final rankings
      const rankedParticipants = session.participants
        .filter(p => p.isActive)
        .sort((a, b) => {
          // Sort by score (desc), then by total answered (desc), then by time spent (asc)
          if (b.score !== a.score) return b.score - a.score
          if (b.totalAnswered !== a.totalAnswered) return b.totalAnswered - a.totalAnswered
          return a.timeSpent - b.timeSpent
        })

      // Update participant rankings
      for (let i = 0; i < rankedParticipants.length; i++) {
        await prisma.liveQuizParticipant.update({
          where: { id: rankedParticipants[i].id },
          data: { rank: i + 1 }
        })
      }

      // Update session status to COMPLETED
      const updatedSession = await prisma.liveQuizSession.update({
        where: { id: sessionId },
        data: {
          status: 'COMPLETED',
          endTime: new Date()
        },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              questions: {
                select: { id: true }
              }
            }
          },
          participants: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              }
            },
            orderBy: { rank: 'asc' }
          }
        }
      })

      // Calculate session statistics
      const stats = {
        totalParticipants: updatedSession.participants.length,
        activeParticipants: updatedSession.participants.filter(p => p.isActive).length,
        averageScore: updatedSession.participants.length > 0 
          ? Math.round(updatedSession.participants.reduce((sum, p) => sum + p.score, 0) / updatedSession.participants.length)
          : 0,
        highestScore: Math.max(...updatedSession.participants.map(p => p.score), 0),
        completionRate: updatedSession.participants.length > 0
          ? Math.round((updatedSession.participants.filter(p => p.totalAnswered > 0).length / updatedSession.participants.length) * 100)
          : 0
      }

      // Broadcast session completed event
      try {
        const { getSocketManager } = await import('@/lib/socket-server')
        const socketManager = getSocketManager()

        if (socketManager) {
          // Notify all participants
          socketManager.broadcastToAll('live-quiz:session-completed', {
            sessionId,
            session: {
              id: updatedSession.id,
              title: updatedSession.title,
              status: updatedSession.status,
              endTime: updatedSession.endTime
            },
            finalRankings: updatedSession.participants
              .filter(p => p.isActive)
              .map(p => ({
                userId: p.userId,
                userName: p.user.name,
                score: p.score,
                rank: p.rank,
                correctAnswers: p.correctAnswers,
                totalAnswered: p.totalAnswered,
                timeSpent: p.timeSpent
              })),
            stats
          })
        }

        // Send individual completion notifications
        if (socketManager) {
          for (const participant of updatedSession.participants) {
            if (participant.isActive) {
              socketManager.sendNotificationToUser(participant.userId, {
                type: 'success',
                title: 'Quiz Completed!',
                message: `${updatedSession.title} has ended. Your final rank: #${participant.rank || 'N/A'}`,
                data: {
                  sessionId,
                  action: 'session-completed',
                  finalRank: participant.rank,
                  finalScore: participant.score
                }
              })
            }
          }
        }
      } catch (socketError) {
        console.warn('Failed to send socket notifications:', socketError)
        // Continue execution even if socket notifications fail
      }

      return APIResponse.success({
        session: {
          ...updatedSession,
          participantCount: updatedSession.participants.length,
          questionCount: updatedSession.quiz.questions.length
        },
        finalRankings: updatedSession.participants
          .filter(p => p.isActive)
          .map(p => ({
            userId: p.userId,
            userName: p.user.name,
            userEmail: p.user.email,
            score: p.score,
            rank: p.rank,
            correctAnswers: p.correctAnswers,
            totalAnswered: p.totalAnswered,
            timeSpent: p.timeSpent,
            joinedAt: p.joinedAt
          })),
        stats
      }, 'Live quiz session completed successfully')

    } catch (error) {
      console.error('Error stopping live quiz session:', error)
      return APIResponse.error('Failed to stop live quiz session', 500)
    }
  }
)
