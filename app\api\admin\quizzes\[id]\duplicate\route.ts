import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params

    // Get the original quiz with all its questions
    const originalQuiz = await prisma.quiz.findUnique({
      where: { id },
      include: {
        questions: {
          orderBy: { order: 'asc' }
        }
      }
    })

    if (!originalQuiz) {
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      )
    }

    // Create the duplicate quiz
    const duplicatedQuiz = await prisma.quiz.create({
      data: {
        title: `${originalQuiz.title} (Copy)`,
        description: originalQuiz.description,
        type: originalQuiz.type,
        difficulty: originalQuiz.difficulty,
        thumbnail: originalQuiz.thumbnail,
        tags: originalQuiz.tags,
        timeLimit: originalQuiz.timeLimit,
        startTime: null, // Reset scheduling
        endTime: null,
        maxAttempts: originalQuiz.maxAttempts,
        passingScore: originalQuiz.passingScore,
        instructions: originalQuiz.instructions,
        isPublished: false, // Always create as draft
        createdBy: session.user.id,
        questions: {
          create: originalQuiz.questions.map(question => ({
            type: question.type,
            text: question.text,
            options: question.options,
            correctAnswer: question.correctAnswer,
            explanation: question.explanation,
            points: question.points,
            difficulty: question.difficulty,
            tags: question.tags,
            image: question.image,
            order: question.order
          }))
        }
      },
      include: {
        questions: true,
        creator: {
          select: { name: true, email: true }
        }
      }
    })

    return NextResponse.json({
      success: true,
      quiz: {
        id: duplicatedQuiz.id,
        title: duplicatedQuiz.title,
        description: duplicatedQuiz.description,
        type: duplicatedQuiz.type,
        difficulty: duplicatedQuiz.difficulty,
        questionCount: duplicatedQuiz.questions.length,
        isPublished: duplicatedQuiz.isPublished,
        createdAt: duplicatedQuiz.createdAt.toISOString(),
        creator: duplicatedQuiz.creator
      }
    })

  } catch (error) {
    console.error('Error duplicating quiz:', error)
    return NextResponse.json(
      { error: 'Failed to duplicate quiz' },
      { status: 500 }
    )
  }
}
