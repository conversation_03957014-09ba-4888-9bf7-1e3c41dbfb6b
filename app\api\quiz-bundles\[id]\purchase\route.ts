import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { quizBundleService } from '@/lib/quiz-bundle-service'
import { razorpayService } from '@/lib/razorpay-service'
import { NotificationEvents } from '@/lib/notification-events'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const purchaseSchema = z.object({
  paymentMethod: z.enum(['razorpay', 'free']).default('razorpay')
})

const verifyPaymentSchema = z.object({
  razorpay_order_id: z.string().min(1, 'Order ID is required'),
  razorpay_payment_id: z.string().min(1, 'Payment ID is required'),
  razorpay_signature: z.string().min(1, 'Signature is required')
})

// POST /api/quiz-bundles/[id]/purchase - Purchase bundle or create payment order
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: purchaseSchema
  },
  async (request: NextRequest, { params, validatedBody, user }) => {
    try {
      const resolvedParams = await params
      const bundleId = resolvedParams?.id as string
      const { paymentMethod } = validatedBody

      if (!bundleId) {
        return APIResponse.error('Bundle ID is required', 400)
      }

      // Get bundle details
      const bundle = await quizBundleService.getBundleById(bundleId, false)

      if (!bundle) {
        return APIResponse.error('Bundle not found', 404)
      }

      if (!bundle.isActive || !bundle.isPublished) {
        return APIResponse.error('Bundle is not available for purchase', 400)
      }

      // Check if user already purchased this bundle
      const existingPurchase = await prisma.quizBundlePurchase.findUnique({
        where: {
          userId_bundleId: {
            userId: user.id,
            bundleId: bundleId
          }
        }
      })

      if (existingPurchase && existingPurchase.status === 'active') {
        return APIResponse.error('You have already purchased this bundle', 400)
      }

      // Handle free bundles
      if (bundle.price === 0 || paymentMethod === 'free') {
        const purchase = await quizBundleService.purchaseBundle(user.id, bundleId)

        // Send notification
        try {
          await NotificationEvents.onBundlePurchased(
            user.id,
            bundle.title,
            bundle.id
          )
        } catch (notificationError) {
          console.error('Error sending bundle purchase notification:', notificationError)
        }

        return APIResponse.success({
          message: 'Bundle purchased successfully',
          purchase: {
            id: purchase.id,
            bundleId: purchase.bundleId,
            status: purchase.status,
            purchasedAt: purchase.purchasedAt
          },
          redirectUrl: `/student/quiz-bundles/${bundle.slug}`
        })
      }

      // Handle paid bundles - create Razorpay order
      const orderData = await razorpayService.createOrder({
        amount: bundle.price,
        currency: 'INR',
        bundleId: bundleId,
        userId: user.id,
        courseName: bundle.title,
        userEmail: user.email || '',
        userName: user.name || 'Student',
        paymentType: 'bundle'
      })

      return APIResponse.success({
        message: 'Payment order created',
        paymentOrder: {
          ...orderData,
          bundle: {
            id: bundle.id,
            title: bundle.title,
            price: bundle.price,
            slug: bundle.slug
          },
          user: {
            name: user.name,
            email: user.email
          }
        }
      })

    } catch (error: any) {
      console.error('Error processing bundle purchase:', error)
      return APIResponse.error('Failed to process bundle purchase', 500)
    }
  }
)

// PUT /api/quiz-bundles/[id]/purchase - Verify payment and complete purchase
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: verifyPaymentSchema
  },
  async (request: NextRequest, { params, validatedBody, user }) => {
    try {
      const resolvedParams = await params
      const bundleId = resolvedParams?.id as string
      const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = validatedBody

      if (!bundleId) {
        return APIResponse.error('Bundle ID is required', 400)
      }

      // Verify payment with Razorpay - this will handle the purchase creation
      const verificationResult = await razorpayService.verifyPayment({
        razorpayOrderId: razorpay_order_id,
        razorpayPaymentId: razorpay_payment_id,
        razorpaySignature: razorpay_signature
      })

      if (!verificationResult.success) {
        return APIResponse.error(verificationResult.message, 400)
      }

      // Get payment details
      const payment = await razorpayService.getPaymentByOrderId(razorpay_order_id)

      if (!payment) {
        return APIResponse.error('Payment record not found', 404)
      }

      // Get the purchase record that was created by the verification process
      const purchase = await prisma.quizBundlePurchase.findUnique({
        where: {
          userId_bundleId: {
            userId: user.id,
            bundleId: bundleId
          }
        }
      })

      if (!purchase) {
        return APIResponse.error('Purchase record not found after payment verification', 500)
      }

      // Get bundle details for notification
      const bundle = await quizBundleService.getBundleById(bundleId, false)

      if (bundle) {
        try {
          // Send purchase notification
          await NotificationEvents.onBundlePurchased(
            user.id,
            bundle.title,
            bundle.id
          )
        } catch (notificationError) {
          console.error('Error sending bundle purchase notification:', notificationError)
        }
      }

      return APIResponse.success({
        message: 'Payment verified and bundle purchased successfully',
        purchase: {
          id: purchase.id,
          bundleId: purchase.bundleId,
          status: purchase.status,
          purchasedAt: purchase.purchasedAt
        },
        paymentId: verificationResult.paymentId,
        redirectUrl: bundle ? `/student/quiz-bundles/${bundle.slug}` : '/student/quiz-bundles'
      })

    } catch (error: any) {
      console.error('Error verifying bundle payment:', error)
      return APIResponse.error('Payment verification failed', 500)
    }
  }
)
