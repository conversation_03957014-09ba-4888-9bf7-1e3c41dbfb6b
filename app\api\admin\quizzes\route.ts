import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { createAPIHandler } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createQuizSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  type: z.enum(['QUIZ', 'TEST_SERIES', 'DAILY_PRACTICE']),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']),
  tags: z.array(z.string()),
  timeLimit: z.number().min(1),
  maxAttempts: z.number().min(1),
  passingScore: z.number().min(0).max(100),
  instructions: z.string().optional(),
  thumbnail: z.string().optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
  isPublished: z.boolean(),
  // Category fields
  subjectId: z.string().optional(),
  chapterId: z.string().optional(),
  topicId: z.string().optional(),
  questions: z.array(z.object({
    type: z.enum(['MCQ', 'TRUE_FALSE', 'SHORT_ANSWER', 'MATCHING']),
    text: z.string().min(1),
    options: z.array(z.string()),
    correctAnswer: z.string(),
    explanation: z.string().optional(),
    points: z.number().min(1),
    image: z.string().optional(),
    order: z.number()
  }))
})

export const POST = createAPIHandler({ requireAuth: true, requireRole: 'ADMIN', validateBody: createQuizSchema }, async (request: NextRequest) => {
  try {
    const session = await auth()
    const validatedData = (request as any).validatedBody || await request.json()

    // Create quiz with questions in a transaction
    const quiz = await prisma.$transaction(async (tx) => {
      // Create the quiz
      const newQuiz = await tx.quiz.create({
        data: {
          title: validatedData.title,
          description: validatedData.description,
          type: validatedData.type,
          difficulty: validatedData.difficulty,
          tags: validatedData.tags,
          timeLimit: validatedData.timeLimit,
          startTime: validatedData.startTime ? new Date(validatedData.startTime) : null,
          endTime: validatedData.endTime ? new Date(validatedData.endTime) : null,
          maxAttempts: validatedData.maxAttempts,
          passingScore: validatedData.passingScore,
          instructions: validatedData.instructions,
          thumbnail: validatedData.thumbnail,
          isPublished: validatedData.isPublished,
          subjectId: validatedData.subjectId,
          chapterId: validatedData.chapterId,
          topicId: validatedData.topicId,
           createdBy: session!.user.id,
        }
      })

      // Create questions
      if (validatedData.questions.length > 0) {
        await tx.question.createMany({
          data: validatedData.questions.map((question: any, index: number) => ({
            quizId: newQuiz.id,
            type: question.type,
            text: question.text,
            options: question.options,
            correctAnswer: question.correctAnswer,
            explanation: question.explanation,
            points: question.points,
            image: question.image,
            order: question.order || index + 1,
          }))
        })
      }

      return newQuiz
    })

    return NextResponse.json({ 
      success: true, 
      quiz: { id: quiz.id, title: quiz.title } 
    })
  } catch (error) {
    console.error('Error creating quiz:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create quiz' },
      { status: 500 }
    )
  }
})

export const GET = createAPIHandler({ requireAuth: true, requireRole: 'ADMIN' }, async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const type = searchParams.get('type')
    const status = searchParams.get('status')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (type && type !== 'all') {
      where.type = type
    }

    if (status === 'published') {
      where.isPublished = true
    } else if (status === 'draft') {
      where.isPublished = false
    }

    const [quizzes, total] = await Promise.all([
      prisma.quiz.findMany({
        where,
        include: {
          creator: {
            select: { name: true, email: true }
          },
          questions: {
            select: { id: true }
          },
          attempts: {
            select: { id: true, score: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.quiz.count({ where })
    ])

    // Calculate statistics for each quiz
    const quizzesWithStats = quizzes.map(quiz => ({
      ...quiz,
      questionCount: quiz.questions.length,
      attemptCount: quiz.attempts.length,
      averageScore: quiz.attempts.length > 0 
        ? Math.round(quiz.attempts.reduce((sum, attempt) => sum + attempt.score, 0) / quiz.attempts.length)
        : 0
    }))

    return NextResponse.json({
      quizzes: quizzesWithStats,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      success: true
    })
  } catch (error) {
    console.error('Error fetching quizzes:', error)

    // Provide more specific error information
    let errorMessage = 'Failed to fetch quizzes'
    let statusCode = 500

    if (error instanceof Error) {
      if (error.message.includes('connect')) {
        errorMessage = 'Database connection failed. Please try again later.'
        statusCode = 503
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Request timed out. Please try again.'
        statusCode = 408
      } else {
        errorMessage = error.message
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
        success: false,
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: statusCode }
    )
  }
})
