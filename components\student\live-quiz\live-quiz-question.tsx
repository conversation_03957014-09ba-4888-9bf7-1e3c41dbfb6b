"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { 
  Clock, 
  Send, 
  CheckCircle, 
  AlertCircle,
  Timer,
  Target,
  Zap
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"

interface Question {
  id: string
  type: 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'MATCHING'
  text: string
  options: string[]
  points: number
  order: number
  image?: string
  hasAnswered: boolean
  userAnswer?: any
}

interface UserParticipation {
  id: string
  currentQuestion: number
  score: number
  correctAnswers: number
  totalAnswered: number
  rank?: number
  timeSpent: number
}

interface LiveQuizQuestionProps {
  question: Question
  sessionId: string
  timeRemaining: number | null
  onAnswerSubmit: (questionId: string, answer: any, timeSpent: number) => Promise<any>
  userParticipation: UserParticipation | null
  onTimeUp: () => void
}

export function LiveQuizQuestion({ 
  question, 
  sessionId, 
  timeRemaining, 
  onAnswerSubmit, 
  userParticipation,
  onTimeUp 
}: LiveQuizQuestionProps) {
  const [selectedAnswer, setSelectedAnswer] = useState<any>(question.userAnswer || "")
  const [submitting, setSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(question.hasAnswered)
  const [startTime] = useState(Date.now())
  const [feedback, setFeedback] = useState<{
    isCorrect: boolean
    pointsEarned: number
  } | null>(null)

  useEffect(() => {
    // Reset state when question changes
    setSelectedAnswer(question.userAnswer || "")
    setSubmitted(question.hasAnswered)
    setFeedback(null)
  }, [question.id])

  useEffect(() => {
    // Auto-submit when time runs out
    if (timeRemaining === 0 && !submitted) {
      handleSubmit(true)
      onTimeUp()
    }
  }, [timeRemaining])

  const handleSubmit = async (autoSubmit = false) => {
    if (submitting || submitted) return
    if (!selectedAnswer && question.type !== 'SHORT_ANSWER') return

    setSubmitting(true)
    
    try {
      const timeSpent = Math.round((Date.now() - startTime) / 1000)
      const result = await onAnswerSubmit(question.id, selectedAnswer, timeSpent)
      
      if (result) {
        setSubmitted(true)
        setFeedback({
          isCorrect: result.submission.isCorrect,
          pointsEarned: result.submission.pointsEarned
        })
        
        if (!autoSubmit) {
          toast.success('Answer submitted successfully!')
        }
      }
    } catch (error) {
      console.error('Error submitting answer:', error)
      if (!autoSubmit) {
        toast.error('Failed to submit answer')
      }
    } finally {
      setSubmitting(false)
    }
  }

  const handleAnswerChange = (value: any) => {
    if (submitted) return
    setSelectedAnswer(value)
  }

  const getTimeProgress = () => {
    if (!timeRemaining || !question) return 100
    // Assuming default time limit if not specified
    const totalTime = 30 // This should come from session settings
    return ((totalTime - timeRemaining) / totalTime) * 100
  }

  const getTimeColor = () => {
    if (!timeRemaining) return "bg-gray-500"
    if (timeRemaining <= 5) return "bg-red-500"
    if (timeRemaining <= 10) return "bg-yellow-500"
    return "bg-green-500"
  }

  const renderQuestionInput = () => {
    switch (question.type) {
      case 'MCQ':
        return (
          <RadioGroup
            value={selectedAnswer}
            onValueChange={handleAnswerChange}
            disabled={submitted}
            className="space-y-3"
          >
            {question.options.map((option, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                  selectedAnswer === option 
                    ? 'border-primary bg-primary/5' 
                    : 'border-border hover:border-primary/50'
                } ${submitted ? 'opacity-60' : ''}`}
              >
                <RadioGroupItem value={option} id={`option-${index}`} />
                <Label 
                  htmlFor={`option-${index}`} 
                  className="flex-1 cursor-pointer text-sm"
                >
                  {option}
                </Label>
              </motion.div>
            ))}
          </RadioGroup>
        )

      case 'TRUE_FALSE':
        return (
          <RadioGroup
            value={selectedAnswer}
            onValueChange={handleAnswerChange}
            disabled={submitted}
            className="space-y-3"
          >
            {['true', 'false'].map((option, index) => (
              <motion.div
                key={option}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                  selectedAnswer === option 
                    ? 'border-primary bg-primary/5' 
                    : 'border-border hover:border-primary/50'
                } ${submitted ? 'opacity-60' : ''}`}
              >
                <RadioGroupItem value={option} id={`tf-${option}`} />
                <Label 
                  htmlFor={`tf-${option}`} 
                  className="flex-1 cursor-pointer text-sm capitalize"
                >
                  {option}
                </Label>
              </motion.div>
            ))}
          </RadioGroup>
        )

      case 'SHORT_ANSWER':
        return (
          <div className="space-y-3">
            <Input
              value={selectedAnswer}
              onChange={(e) => handleAnswerChange(e.target.value)}
              placeholder="Type your answer here..."
              disabled={submitted}
              className="text-base"
            />
          </div>
        )

      case 'MATCHING':
        // Simplified matching - would need more complex UI for full matching
        return (
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Select the correct matches (simplified view)
            </p>
            {question.options.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Checkbox
                  checked={Array.isArray(selectedAnswer) && selectedAnswer.includes(option)}
                  onCheckedChange={(checked) => {
                    if (submitted) return
                    const current = Array.isArray(selectedAnswer) ? selectedAnswer : []
                    if (checked) {
                      handleAnswerChange([...current, option])
                    } else {
                      handleAnswerChange(current.filter((item: string) => item !== option))
                    }
                  }}
                  disabled={submitted}
                />
                <Label className="text-sm">{option}</Label>
              </div>
            ))}
          </div>
        )

      default:
        return <div>Unsupported question type</div>
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="glass">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="text-sm">
                <Target className="h-3 w-3 mr-1" />
                {question.points} points
              </Badge>
              <Badge variant="outline" className="text-sm">
                Question {question.order + 1}
              </Badge>
              {submitted && (
                <Badge 
                  className={feedback?.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                >
                  {feedback?.isCorrect ? (
                    <>
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Correct (+{feedback.pointsEarned})
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Incorrect
                    </>
                  )}
                </Badge>
              )}
            </div>
            
            {timeRemaining !== null && (
              <div className="flex items-center gap-2">
                <Timer className="h-4 w-4 text-muted-foreground" />
                <span className={`text-sm font-mono ${timeRemaining <= 10 ? 'text-red-500' : ''}`}>
                  {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}
                </span>
              </div>
            )}
          </div>
          
          {timeRemaining !== null && (
            <div className="space-y-2">
              <Progress 
                value={getTimeProgress()} 
                className="h-2"
              />
            </div>
          )}
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Question Text */}
          <div>
            <CardTitle className="text-lg mb-3">{question.text}</CardTitle>
            {question.image && (
              <div className="mb-4">
                <img 
                  src={question.image} 
                  alt="Question image" 
                  className="max-w-full h-auto rounded-lg border"
                />
              </div>
            )}
          </div>

          {/* Answer Input */}
          <div className="space-y-4">
            {renderQuestionInput()}
          </div>

          {/* Submit Button */}
          <div className="flex justify-between items-center pt-4">
            <div className="text-sm text-muted-foreground">
              {submitted ? (
                <span className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Answer submitted
                </span>
              ) : (
                <span>Select your answer and submit</span>
              )}
            </div>
            
            {!submitted && (
              <Button
                onClick={() => handleSubmit()}
                disabled={submitting || !selectedAnswer}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Submit Answer
                  </>
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
