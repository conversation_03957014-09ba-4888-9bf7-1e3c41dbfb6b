'use client'

import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeftIcon, BookOpenIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'
import RoadmapVisualization from '@/components/student/roadmap/roadmap-visualization'
import { toast } from 'react-hot-toast'

interface Course {
  id: string
  title: string
  slug: string
  hasRoadmap: boolean
  roadmapTitle?: string
  roadmapDescription?: string
}

interface Mission {
  id: string
  title: string
  description?: string
  icon?: string
  color?: string
  order: number
  xPosition?: number
  yPosition?: number
  isRequired: boolean
  pointsReward: number
  badgeReward?: string
  estimatedTime?: string
  contents: MissionContent[]
  prerequisites: string[]
  progress?: MissionProgress
}

interface MissionContent {
  id: string
  contentId: string
  contentType: 'LESSON' | 'QUIZ' | 'ASSIGNMENT' | 'DISCUSSION'
  order: number
  isRequired: boolean
  title?: string
}

interface MissionProgress {
  id: string
  isStarted: boolean
  isCompleted: boolean
  completionRate: number
  pointsEarned: number
  startedAt?: string
  completedAt?: string
}

interface UserProgress {
  totalPoints: number
  completedMissions: number
  currentStreak: number
}

export default function CourseRoadmapPage() {
  const params = useParams()
  const router = useRouter()
  const courseSlug = params?.slug as string

  const [course, setCourse] = useState<Course | null>(null)
  const [missions, setMissions] = useState<Mission[]>([])
  const [userProgress, setUserProgress] = useState<UserProgress>({
    totalPoints: 0,
    completedMissions: 0,
    currentStreak: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (courseSlug) {
      fetchRoadmapData()
    }
  }, [courseSlug])

  const fetchRoadmapData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch roadmap data directly (includes course info)
      const roadmapResponse = await fetch(`/api/student/courses/${courseSlug}/roadmap`)
      if (!roadmapResponse.ok) {
        const errorData = await roadmapResponse.json()
        throw new Error(errorData.message || 'Failed to fetch roadmap data')
      }
      const roadmapData = await roadmapResponse.json()

      // Set course data from roadmap response
      setCourse(roadmapData.data.course)
      setMissions(roadmapData.data.missions || [])
      setUserProgress(roadmapData.data.userProgress || {
        totalPoints: 0,
        completedMissions: 0,
        currentStreak: 0
      })

    } catch (error: any) {
      console.error('Error fetching roadmap data:', error)
      setError(error.message || 'Failed to load roadmap')
      toast.error('Failed to load course roadmap')
    } finally {
      setLoading(false)
    }
  }

  const handleMissionClick = (mission: Mission) => {
    // Track mission interaction
  }

  const handleStartMission = async (missionId: string) => {
    try {
      // Start or continue mission
      const response = await fetch(`/api/student/courses/${courseSlug}/roadmap/missions/${missionId}/start`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to start mission')
      }

      const data = await response.json()
      
      // Navigate to first content item or mission overview
      if (data.data.nextContentUrl) {
        router.push(data.data.nextContentUrl)
      } else {
        // Navigate to course learning page
        router.push(`/student/courses/${courseSlug}`)
      }

    } catch (error: any) {
      console.error('Error starting mission:', error)
      toast.error('Failed to start mission')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading roadmap...</p>
        </div>
      </div>
    )
  }

  if (error || !course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <BookOpenIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {error || 'Roadmap Not Available'}
          </h1>
          <p className="text-gray-600 mb-6">
            {error || 'This course does not have a learning roadmap configured.'}
          </p>
          <Link href={`/student/courses/${courseSlug}`}>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <ArrowLeftIcon className="w-5 h-5 mr-2 inline" />
              Back to Course
            </motion.button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href={`/student/courses/${courseSlug}`}>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-2 bg-white/60 backdrop-blur-xl rounded-xl border border-white/20 shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <ArrowLeftIcon className="w-5 h-5 text-gray-600" />
                </motion.button>
              </Link>
              
              <div>
                <h1 className="text-xl font-bold text-gray-900">Learning Roadmap</h1>
                <p className="text-sm text-gray-600">{course.title}</p>
              </div>
            </div>

            <Link href={`/student/courses/${courseSlug}`}>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <BookOpenIcon className="w-4 h-4 mr-2 inline" />
                Course Content
              </motion.button>
            </Link>
          </div>
        </div>
      </div>

      {/* Roadmap Visualization */}
      <RoadmapVisualization
        courseId={course.id}
        courseTitle={course.title}
        roadmapTitle={course.roadmapTitle}
        roadmapDescription={course.roadmapDescription}
        missions={missions}
        userProgress={userProgress}
        onMissionClick={handleMissionClick}
        onStartMission={handleStartMission}
      />
    </div>
  )
}
