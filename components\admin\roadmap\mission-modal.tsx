'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  XMarkIcon,
  PlusIcon,
  TrashIcon,
  StarIcon,
  ClockIcon,
  CheckCircleIcon,
  BookOpenIcon,
  QuestionMarkCircleIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { toast } from 'react-hot-toast'

interface Mission {
  id: string
  title: string
  description?: string
  icon?: string
  color?: string
  order: number
  xPosition?: number
  yPosition?: number
  isRequired: boolean
  pointsReward: number
  badgeReward?: string
  estimatedTime?: string
  contents: MissionContent[]
  prerequisites: string[]
}

interface MissionContent {
  id: string
  contentId: string
  contentType: 'LESSON' | 'QUIZ' | 'ASSIGNMENT' | 'DISCUSSION'
  order: number
  isRequired: boolean
  title?: string
}

interface CourseContent {
  id: string
  title: string
  type: 'LESSON' | 'QUIZ' | 'ASSIGNMENT' | 'DISCUSSION'
  sectionTitle?: string
  chapterTitle?: string
}

interface MissionModalProps {
  isOpen: boolean
  onClose: () => void
  mission?: Mission | null
  courseId: string
  existingMissions: Mission[]
  onSave: (mission: Mission) => void
}

const MISSION_COLORS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', 
  '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
]

const MISSION_ICONS = ['🎯', '🚀', '⭐', '🏆', '💎', '🔥', '⚡', '🎨', '🧠', '💪']

export default function MissionModal({
  isOpen,
  onClose,
  mission,
  courseId,
  existingMissions,
  onSave
}: MissionModalProps) {
  const [formData, setFormData] = useState<Mission>({
    id: '',
    title: '',
    description: '',
    icon: '🎯',
    color: '#3B82F6',
    order: 0,
    isRequired: true,
    pointsReward: 100,
    badgeReward: '',
    estimatedTime: '',
    contents: [],
    prerequisites: []
  })
  const [availableContent, setAvailableContent] = useState<CourseContent[]>([])
  const [loading, setLoading] = useState(false)
  const [showContentSelector, setShowContentSelector] = useState(false)

  useEffect(() => {
    if (mission) {
      setFormData(mission)
    } else {
      setFormData({
        id: '',
        title: '',
        description: '',
        icon: '🎯',
        color: '#3B82F6',
        order: existingMissions.length + 1,
        isRequired: true,
        pointsReward: 100,
        badgeReward: '',
        estimatedTime: '',
        contents: [],
        prerequisites: []
      })
    }
  }, [mission, existingMissions])

  useEffect(() => {
    if (isOpen) {
      fetchCourseContent()
    }
  }, [isOpen, courseId])

  const fetchCourseContent = async () => {
    try {
      const response = await fetch(`/api/admin/courses/${courseId}/content`)
      if (response.ok) {
        const result = await response.json()
        setAvailableContent(result.data?.content || [])
      }
    } catch (error) {
      console.error('Failed to fetch course content:', error)
    }
  }

  const handleInputChange = (field: keyof Mission, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleAddContent = (content: CourseContent) => {
    const newContent: MissionContent = {
      id: `content_${Date.now()}`,
      contentId: content.id,
      contentType: content.type,
      order: formData.contents.length + 1,
      isRequired: true,
      title: content.title
    }
    
    setFormData(prev => ({
      ...prev,
      contents: [...prev.contents, newContent]
    }))
    setShowContentSelector(false)
  }

  const handleRemoveContent = (contentId: string) => {
    setFormData(prev => ({
      ...prev,
      contents: prev.contents.filter(c => c.id !== contentId)
    }))
  }

  const handleToggleContentRequired = (contentId: string) => {
    setFormData(prev => ({
      ...prev,
      contents: prev.contents.map(c => 
        c.id === contentId ? { ...c, isRequired: !c.isRequired } : c
      )
    }))
  }

  const handleSave = async () => {
    if (!formData.title.trim()) {
      toast.error('Mission title is required')
      return
    }

    if (formData.contents.length === 0) {
      toast.error('Please add at least one content item to the mission')
      return
    }

    setLoading(true)
    try {
      onSave(formData)
      onClose()
    } catch (error) {
      toast.error('Failed to save mission')
    } finally {
      setLoading(false)
    }
  }

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'LESSON': return <BookOpenIcon className="h-4 w-4" />
      case 'QUIZ': return <QuestionMarkCircleIcon className="h-4 w-4" />
      case 'ASSIGNMENT': return <DocumentTextIcon className="h-4 w-4" />
      case 'DISCUSSION': return <ChatBubbleLeftRightIcon className="h-4 w-4" />
      default: return <BookOpenIcon className="h-4 w-4" />
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">
              {mission ? 'Edit Mission' : 'Create New Mission'}
            </h2>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <XMarkIcon className="h-5 w-5" />
            </Button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mission Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="e.g., JavaScript Fundamentals"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Estimated Time
                  </label>
                  <input
                    type="text"
                    value={formData.estimatedTime}
                    onChange={(e) => handleInputChange('estimatedTime', e.target.value)}
                    placeholder="e.g., 2 hours"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe what students will learn in this mission..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Visual Customization */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mission Icon
                  </label>
                  <div className="grid grid-cols-5 gap-2">
                    {MISSION_ICONS.map((icon) => (
                      <button
                        key={icon}
                        type="button"
                        onClick={() => handleInputChange('icon', icon)}
                        className={`p-2 text-xl border rounded-lg hover:bg-gray-50 ${
                          formData.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                        }`}
                      >
                        {icon}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mission Color
                  </label>
                  <div className="grid grid-cols-5 gap-2">
                    {MISSION_COLORS.map((color) => (
                      <button
                        key={color}
                        type="button"
                        onClick={() => handleInputChange('color', color)}
                        className={`w-8 h-8 rounded-full border-2 ${
                          formData.color === color ? 'border-gray-800' : 'border-gray-300'
                        }`}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>
              </div>

              {/* Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Required Mission</label>
                    <p className="text-xs text-gray-500">Students must complete this to progress</p>
                  </div>
                  <Switch
                    checked={formData.isRequired}
                    onCheckedChange={(checked) => handleInputChange('isRequired', checked)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Points Reward
                  </label>
                  <input
                    type="number"
                    value={formData.pointsReward}
                    onChange={(e) => handleInputChange('pointsReward', parseInt(e.target.value) || 0)}
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Content Items */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Mission Content</h3>
                  <Button
                    type="button"
                    onClick={() => setShowContentSelector(true)}
                    size="sm"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Content
                  </Button>
                </div>

                {formData.contents.length === 0 ? (
                  <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                    <BookOpenIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No content added yet</p>
                    <p className="text-sm text-gray-500">Add lessons, quizzes, or assignments to this mission</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {formData.contents.map((content, index) => (
                      <div
                        key={content.id}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="text-gray-500">
                            {getContentIcon(content.contentType)}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{content.title}</p>
                            <p className="text-sm text-gray-500">{content.contentType}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={content.isRequired}
                            onCheckedChange={() => handleToggleContentRequired(content.id)}
                          />
                          <span className="text-xs text-gray-500">
                            {content.isRequired ? 'Required' : 'Optional'}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveContent(content.id)}
                          >
                            <TrashIcon className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={loading}>
              {loading ? 'Saving...' : (mission ? 'Update Mission' : 'Create Mission')}
            </Button>
          </div>
        </motion.div>

        {/* Content Selector Modal */}
        {showContentSelector && (
          <ContentSelectorModal
            availableContent={availableContent}
            selectedContent={formData.contents.map(c => c.contentId)}
            onSelect={handleAddContent}
            onClose={() => setShowContentSelector(false)}
          />
        )}
      </div>
    </AnimatePresence>
  )
}

interface ContentSelectorModalProps {
  availableContent: CourseContent[]
  selectedContent: string[]
  onSelect: (content: CourseContent) => void
  onClose: () => void
}

function ContentSelectorModal({ 
  availableContent, 
  selectedContent, 
  onSelect, 
  onClose 
}: ContentSelectorModalProps) {
  const getContentIcon = (type: string) => {
    switch (type) {
      case 'LESSON': return <BookOpenIcon className="h-4 w-4" />
      case 'QUIZ': return <QuestionMarkCircleIcon className="h-4 w-4" />
      case 'ASSIGNMENT': return <DocumentTextIcon className="h-4 w-4" />
      case 'DISCUSSION': return <ChatBubbleLeftRightIcon className="h-4 w-4" />
      default: return <BookOpenIcon className="h-4 w-4" />
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[80vh] overflow-hidden"
      >
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-xl font-bold text-gray-900">Select Content</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <XMarkIcon className="h-5 w-5" />
          </Button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(80vh-140px)]">
          <div className="space-y-2">
            {availableContent.length === 0 ? (
              <div className="text-center py-8">
                <BookOpenIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No content available</p>
                <p className="text-sm text-gray-500">Add lessons, quizzes, or assignments to this course first</p>
              </div>
            ) : (
              availableContent
                .filter(content => !selectedContent.includes(content.id))
                .map((content) => (
                  <button
                    key={content.id}
                    onClick={() => onSelect(content)}
                    className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
                  >
                    <div className="text-gray-500">
                      {getContentIcon(content.type)}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{content.title}</p>
                      <p className="text-sm text-gray-500">
                        {content.type} {content.sectionTitle && `• ${content.sectionTitle}`}
                      </p>
                    </div>
                  </button>
                ))
            )}
          </div>
        </div>
      </motion.div>
    </div>
  )
}
