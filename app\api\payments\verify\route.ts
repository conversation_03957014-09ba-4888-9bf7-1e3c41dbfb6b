import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { razorpayService } from '@/lib/razorpay-service'
import { NotificationEvents } from '@/lib/notification-events'
import { z } from 'zod'

const verifyPaymentSchema = z.object({
  razorpay_order_id: z.string().min(1, 'Order ID is required'),
  razorpay_payment_id: z.string().min(1, 'Payment ID is required'),
  razorpay_signature: z.string().min(1, 'Signature is required')
})

// POST /api/payments/verify - Verify Razorpay payment and complete enrollment
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: verifyPaymentSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = validatedBody

      // Verify payment with Razorpay service
      const verificationResult = await razorpayService.verifyPayment({
        razorpayOrderId: razorpay_order_id,
        razorpayPaymentId: razorpay_payment_id,
        razorpaySignature: razorpay_signature
      })

      if (!verificationResult.success) {
        return APIResponse.error(verificationResult.message, 400)
      }

      // Get payment details for notifications
      const payment = await razorpayService.getPaymentByOrderId(razorpay_order_id)
      
      if (payment && payment.course) {
        try {
          // Send enrollment notification
          await NotificationEvents.onCourseEnrolled(
            user.id,
            payment.course.title,
            payment.course.id
          )
        } catch (notificationError) {
          console.error('Error sending enrollment notification:', notificationError)
          // Don't fail the payment verification if notification fails
        }
      }

      return APIResponse.success({
        message: 'Payment verified successfully',
        paymentId: verificationResult.paymentId,
        enrollmentId: verificationResult.enrollmentId,
        redirectUrl: payment?.course?.slug ? `/student/courses/${payment.course.slug}` : '/student/my-courses'
      })

    } catch (error) {
      console.error('Error verifying payment:', error)
      return APIResponse.error('Payment verification failed', 500)
    }
  }
)
