import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { getSocketClient } from '@/lib/socket-client'

// POST /api/student/live-quiz/sessions/[id]/leave - Leave live quiz session
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Get session details
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        select: {
          id: true,
          title: true,
          status: true
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Get user's participation
      const participation = await prisma.liveQuizParticipant.findUnique({
        where: {
          sessionId_userId: {
            sessionId,
            userId: user.id
          }
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      if (!participation) {
        return APIResponse.error('Not participating in this session', 400, 'NOT_PARTICIPATING')
      }

      if (!participation.isActive) {
        return APIResponse.error('Already left this session', 400, 'ALREADY_LEFT')
      }

      // Update participation to inactive
      const updatedParticipation = await prisma.liveQuizParticipant.update({
        where: { id: participation.id },
        data: {
          isActive: false,
          leftAt: new Date()
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      // Get updated participant count
      const remainingParticipantCount = await prisma.liveQuizParticipant.count({
        where: {
          sessionId,
          isActive: true
        }
      })

      // Broadcast participant left event
      try {
        const socketClient = getSocketClient()
        
        // Notify session room about participant leaving
        socketClient.broadcastLiveQuizEvent('live-quiz:participant-left', {
          sessionId,
          participant: {
            id: participation.id,
            userId: participation.userId,
            userName: participation.user.name,
            leftAt: updatedParticipation.leftAt,
            finalScore: participation.score,
            finalRank: participation.rank
          },
          session: {
            id: session.id,
            title: session.title,
            participantCount: remainingParticipantCount
          }
        })

        // Send confirmation notification to the user
        socketClient.sendNotification({
          targetUserId: user.id,
          type: 'info',
          title: 'Left Session',
          message: `You have left ${session.title}. Your progress has been saved.`,
          data: {
            sessionId,
            action: 'left-session',
            finalScore: participation.score,
            finalRank: participation.rank
          }
        })

      } catch (socketError) {
        console.warn('Failed to send socket notifications:', socketError)
        // Continue execution even if socket notifications fail
      }

      return APIResponse.success({
        participation: {
          id: updatedParticipation.id,
          sessionId: updatedParticipation.sessionId,
          userId: updatedParticipation.userId,
          joinedAt: updatedParticipation.joinedAt,
          leftAt: updatedParticipation.leftAt,
          finalScore: updatedParticipation.score,
          finalRank: updatedParticipation.rank,
          correctAnswers: updatedParticipation.correctAnswers,
          totalAnswered: updatedParticipation.totalAnswered,
          timeSpent: updatedParticipation.timeSpent,
          isActive: updatedParticipation.isActive
        },
        session: {
          id: session.id,
          title: session.title,
          status: session.status,
          remainingParticipants: remainingParticipantCount
        }
      }, 'Successfully left live quiz session')

    } catch (error) {
      console.error('Error leaving live quiz session:', error)
      return APIResponse.error('Failed to leave live quiz session', 500)
    }
  }
)
