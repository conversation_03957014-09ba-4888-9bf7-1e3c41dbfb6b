'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calculator,
  Stethoscope,
  Building,
  GraduationCap,
  Users,
  Clock,
  Star,
  ArrowRight,
  Filter,
  BookOpen,
  ExternalLink,
  Briefcase,
  Laptop,
  Globe,
  DollarSign
} from 'lucide-react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';
import { AnimatedCard } from '@/components/ui/animated-card';
import { AnimatedButton } from '@/components/ui/animated-button';
import { cn } from '@/lib/utils';
import { useSession } from 'next-auth/react';


interface Course {
  id: string;
  productId: string;
  title: string;
  description?: string;
  category?: string;
  price: number;
  originalPrice?: number;
  duration?: string;
  studentsCount?: number;
  rating?: number;
  thumbnailImage?: string;
  features: string[];
  instructor?: string | {
    id: string;
    name: string;
    image?: string;
  };
  slug: string;
  isEnrolled?: boolean;
}

// Static fallback courses for when API is not available
const fallbackCourses: Course[] = [
  {
    id: '1',
    productId: 'fallback-1',
    title: 'JEE Main & Advanced Complete Course',
    description: 'Comprehensive preparation for JEE with expert faculty and AI-powered practice',
    category: 'engineering',
    price: 99,
    originalPrice: 999,
    duration: '12 months',
    studentsCount: 50000,
    rating: 4.9,
    thumbnailImage: '/api/placeholder/400/250',
    features: ['Live Classes', 'Mock Tests', 'Doubt Solving', 'Study Material'],
    instructor: 'Expert Faculty',
    slug: 'jee-main-advanced-complete-course'
  },
  {
    id: '2',
    productId: 'fallback-2',
    title: 'NEET Complete Preparation',
    description: 'Master Biology, Chemistry, and Physics for NEET with proven strategies',
    category: 'medical',
    price: 99,
    originalPrice: 899,
    duration: '10 months',
    studentsCount: 75000,
    rating: 4.8,
    thumbnailImage: '/api/placeholder/400/250',
    features: ['Expert Faculty', 'Previous Year Papers', 'Biology Focus', 'Chemistry Labs'],
    instructor: 'Medical Experts',
    slug: 'neet-complete-preparation'
  },
  {
    id: '3',
    productId: 'fallback-3',
    title: 'UPSC Civil Services Complete',
    description: 'Comprehensive UPSC preparation with current affairs and answer writing',
    category: 'government',
    price: 149,
    originalPrice: 1299,
    duration: '18 months',
    studentsCount: 30000,
    rating: 4.9,
    thumbnailImage: '/api/placeholder/400/250',
    features: ['Current Affairs', 'Answer Writing', 'Interview Prep', 'Optional Subjects'],
    instructor: 'IAS Officers',
    slug: 'upsc-civil-services-complete'
  }
];

// Icon mapping for categories
const categoryIcons: Record<string, any> = {
  'engineering': Calculator,
  'medical': Stethoscope,
  'government': Building,
  'teaching': Users,
  'business': Briefcase,
  'technology': Laptop,
  'language': Globe,
  'finance': DollarSign,
  'default': BookOpen
};

// Color mapping for categories
const categoryColors: Record<string, string> = {
  'engineering': 'blue',
  'medical': 'pink',
  'government': 'green',
  'teaching': 'orange',
  'business': 'purple',
  'technology': 'indigo',
  'language': 'teal',
  'finance': 'yellow',
  'default': 'violet'
};

export function CoursesSection() {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [courses, setCourses] = useState<Course[]>(fallbackCourses);
  const [categories, setCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [userEnrollments, setUserEnrollments] = useState<string[]>([]);
  const { data: session } = useSession();
  const { elementRef, isVisible } = useIntersectionObserver({
    threshold: 0.2,
    freezeOnceVisible: true,
  });

  // Fetch categories from public API
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setCategoriesLoading(true);
        const response = await fetch('/api/public/categories');
        if (response.ok) {
          const result = await response.json();
          const data = result.data || result;
          if (data.categories && data.categories.length > 0) {
            // Add "All Courses" category at the beginning
            const allCategory = {
              id: 'all',
              name: 'All Courses',
              icon: 'GraduationCap',
              color: 'violet',
              courseCount: data.categories.reduce((sum: number, cat: any) => sum + (cat.courseCount || 0), 0)
            };
            setCategories([allCategory, ...data.categories]);
          }
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        // Set fallback categories
        const fallbackCategories = [
          { id: 'all', name: 'All Courses', icon: 'GraduationCap', color: 'violet', courseCount: 0 },
          { id: 'engineering', name: 'Engineering', icon: 'Calculator', color: 'blue', courseCount: 0 },
          { id: 'medical', name: 'Medical', icon: 'Stethoscope', color: 'pink', courseCount: 0 },
          { id: 'government', name: 'Government', icon: 'Building', color: 'green', courseCount: 0 },
          { id: 'teaching', name: 'Teaching', icon: 'Users', color: 'orange', courseCount: 0 },
        ];
        setCategories(fallbackCategories);
      } finally {
        setCategoriesLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Fetch courses from public API (no auth required)
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/public/courses?limit=6');

        if (response.ok) {
          const result = await response.json();
          // Handle APIResponse format: { success: true, data: { courses: [...] } }
          const data = result.data || result;
          if (data.courses && data.courses.length > 0) {
            setCourses(data.courses);
          }
        }
      } catch (error) {
        console.error('Error fetching courses:', error);
        // Keep fallback courses on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourses();
  }, []);

  // Fetch user enrollments if signed in
  useEffect(() => {
    const fetchUserEnrollments = async () => {
      if (!session) {
        setUserEnrollments([]);
        return;
      }

      try {
        const response = await fetch('/api/courses/my-courses?status=all&sync=true');
        if (response.ok) {
          const result = await response.json();
          const data = result.data || result;
          if (data.enrollments) {
            // Extract course IDs from enrollments
            const enrolledCourseIds = data.enrollments.map((enrollment: any) => enrollment.courseId);
            setUserEnrollments(enrolledCourseIds);
          }
        }
      } catch (error) {
        console.error('Error fetching user enrollments:', error);
      }
    };

    fetchUserEnrollments();
  }, [session]);

  const filteredCourses = (activeCategory === 'all'
    ? courses
    : courses.filter(course => course.category?.toLowerCase() === activeCategory.toLowerCase()))
    .map(course => ({
      ...course,
      isEnrolled: userEnrollments.includes(course.id)
    }));

  const handleEnrollClick = (course: Course) => {
    if (!session) {
      // Redirect to sign-in for non-authenticated users
      window.location.href = '/auth/signin';
      return;
    }

    if (course.isEnrolled) {
      // Redirect to course learning page for enrolled users
      window.location.href = `/student/courses/${course.slug}`;
    } else {
      // Redirect to student courses page to enroll
      window.location.href = '/student/courses';
    }
  };

  const handleViewCourse = (course: Course) => {
    // Navigate to course details/learning page based on enrollment status
    if (course.isEnrolled) {
      window.location.href = `/student/courses/${course.slug}`;
    } else {
      // Navigate to course preview page for non-enrolled users
      window.location.href = `/courses/${course.slug}`;
    }
  };

  return (
    <section ref={elementRef as React.RefObject<HTMLElement>} className="py-20 bg-gradient-to-b from-violet-50 to-white dark:from-gray-800 dark:to-gray-900 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isVisible ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass text-sm font-medium text-violet-700 dark:text-violet-300 mb-6"
          >
            <Filter className="w-4 h-4" />
            Choose Your Path
          </motion.div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Explore Our{' '}
            <span className="text-gradient bg-gradient-primary">Courses</span>
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Comprehensive courses designed by experts to help you crack any Indian competitive exam
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categoriesLoading ? (
            // Loading skeleton for categories
            Array.from({ length: 5 }).map((_, index) => (
              <div
                key={`category-skeleton-${index}`}
                className="h-12 w-32 bg-gray-200 dark:bg-gray-700 animate-pulse rounded-2xl"
              />
            ))
          ) : (
            categories.map((category, index) => {
              const IconComponent = category.icon === 'GraduationCap'
                ? GraduationCap
                : categoryIcons[category.name?.toLowerCase()] || categoryIcons.default;

              return (
                <motion.button
                  key={category.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={isVisible ? { opacity: 1, scale: 1 } : {}}
                  transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                  onClick={() => setActiveCategory(category.id)}
                  className={cn(
                    'flex items-center gap-2 px-6 py-3 rounded-2xl font-medium transition-all duration-300',
                    activeCategory === category.id
                      ? 'bg-gradient-primary text-white shadow-glow'
                      : 'glass hover:shadow-lg text-gray-700 dark:text-gray-300'
                  )}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <IconComponent className="w-5 h-5" />
                  {category.name}
                </motion.button>
              );
            })
          )}
        </motion.div>

        {/* Courses Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {isLoading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <motion.div
                  key={`skeleton-${index}`}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="group"
                >
                  <div className="h-full overflow-hidden p-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-gray-800/20 shadow-xl">
                    <div className="h-48 bg-gray-200 dark:bg-gray-700 animate-pulse" />
                    <div className="p-6 space-y-4">
                      <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" />
                      <div className="flex gap-2">
                        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse" />
                        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse" />
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse" />
                        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse" />
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : filteredCourses.length > 0 ? (
              filteredCourses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group cursor-pointer"
                onClick={() => handleViewCourse(course)}
              >
                <AnimatedCard
                  variant="glass"
                  hover="lift"
                  className="h-full overflow-hidden p-0"
                >
                  {/* Course Image */}
                  <div className="relative h-48 bg-gradient-to-br from-violet-400 to-purple-600 overflow-hidden">
                    {course.thumbnailImage ? (
                      <img
                        src={course.thumbnailImage}
                        alt={course.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback to gradient background if image fails to load
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <BookOpen className="h-16 w-16 text-white/80" />
                      </div>
                    )}
                    <div className="absolute inset-0 bg-black/20" />
                    <div className="absolute top-4 left-4 flex gap-2">
                      <span className="px-2 py-1 text-xs font-medium bg-white/20 backdrop-blur-sm rounded-lg text-white capitalize">
                        {course.category || 'Professional Course'}
                      </span>
                      {course.originalPrice && course.originalPrice > course.price && (
                        <span className="px-2 py-1 text-xs font-bold bg-red-500 text-white rounded-lg">
                          {Math.round(((course.originalPrice - course.price) / course.originalPrice) * 100)}% OFF
                        </span>
                      )}
                    </div>
                    <div className="absolute top-4 right-4 flex items-center gap-1 px-2 py-1 bg-white/20 backdrop-blur-sm rounded-lg text-white text-sm">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      {course.rating || '4.5'}
                    </div>
                  </div>

                  {/* Course Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-violet-600 transition-colors">
                      {course.title}
                    </h3>
                    
                    {course.description ? (
                      <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                        {course.description}
                      </p>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-500 mb-4 italic">
                        Professional course designed to enhance your skills and knowledge.
                      </p>
                    )}

                    {/* Features */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {course.features && course.features.length > 0 ? (
                        course.features.slice(0, 3).map((feature, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 text-xs bg-violet-100 dark:bg-violet-900 text-violet-700 dark:text-violet-300 rounded-lg"
                          >
                            {feature}
                          </span>
                        ))
                      ) : (
                        // Default features when none are provided
                        <>
                          <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-lg">
                            Expert Instruction
                          </span>
                          <span className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-lg">
                            Certificate
                          </span>
                          <span className="px-2 py-1 text-xs bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 rounded-lg">
                            Online Access
                          </span>
                        </>
                      )}
                    </div>

                    {/* Course Stats */}
                    <div className="flex items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-400 flex-wrap">
                      {/* Always show course type */}
                      <div className="flex items-center gap-1">
                        <BookOpen className="w-4 h-4" />
                        <span>Online Course</span>
                      </div>

                      {course.duration ? (
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {course.duration}
                        </div>
                      ) : (
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>Self-paced</span>
                        </div>
                      )}

                      {course.studentsCount ? (
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          {course.studentsCount.toLocaleString()}+ students
                        </div>
                      ) : (
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          <span>Popular course</span>
                        </div>
                      )}

                      {course.instructor && (
                        <div className="flex items-center gap-1">
                          <GraduationCap className="w-4 h-4" />
                          {typeof course.instructor === 'string'
                            ? course.instructor
                            : course.instructor.name
                          }
                        </div>
                      )}
                    </div>

                    {/* Price and CTA */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-gray-900 dark:text-white">
                          ₹{course.price}
                        </span>
                        {course.originalPrice && course.originalPrice > course.price && (
                          <span className="text-sm text-gray-500 line-through">
                            ₹{course.originalPrice}
                          </span>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <AnimatedButton
                          variant="outline"
                          size="sm"
                          className="group"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewCourse(course);
                          }}
                        >
                          <ExternalLink className="w-4 h-4" />
                        </AnimatedButton>
                        <AnimatedButton
                          variant={course.isEnrolled ? "secondary" : "primary"}
                          size="sm"
                          className="group"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEnrollClick(course);
                          }}
                        >
                          {!session
                            ? 'Sign In'
                            : course.isEnrolled
                              ? 'Continue Learning'
                              : 'Buy Now'
                          }
                          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                        </AnimatedButton>
                      </div>
                    </div>
                  </div>
                </AnimatedCard>
              </motion.div>
              ))
            ) : (
              // Empty state
              <div className="col-span-full flex flex-col items-center justify-center py-16 text-center">
                <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full flex items-center justify-center mb-6">
                  <BookOpen className="h-12 w-12 text-blue-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  No courses found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 max-w-md">
                  We&apos;re working on adding more courses. Check back soon!
                </p>
              </div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-12"
        >
          <AnimatedButton
            variant="outline"
            size="lg"
            className="group"
            onClick={() => {
              window.location.href = '/courses';
            }}
          >
            Explore All Courses
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </AnimatedButton>
        </motion.div>
      </div>
    </section>
  );
}
