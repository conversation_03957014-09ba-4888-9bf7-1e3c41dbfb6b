"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { FloatingThemeToggle } from "@/components/ui/theme-toggle"
import { LayoutDashboard } from "lucide-react"

export function LandingHeader() {
  const { data: session } = useSession()
  const router = useRouter()

  const handleDashboardClick = () => {
    if (session?.user.role === 'ADMIN') {
      router.push('/admin')
    } else {
      router.push('/student')
    }
  }

  if (!session) {
    return (
      <div className="fixed top-4 right-4 z-50">
        <FloatingThemeToggle />
      </div>
    )
  }

  return (
    <div className="fixed top-6 right-28 z-50 flex items-center gap-3">
      <Button
        onClick={handleDashboardClick}
        variant="outline"
        size="sm"
        className="glass bg-white/10 dark:bg-gray-900/10 backdrop-blur-sm border-white/20 dark:border-gray-800/20 hover:bg-white/20 dark:hover:bg-gray-800/20 transition-all duration-300 shadow-lg"
      >
        <LayoutDashboard className="w-4 h-4 mr-2" />
        Dashboard
      </Button>
      <FloatingThemeToggle />
    </div>
  )
}
