import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { signHmacJwt } from '@/lib/jwt'

export async function GET(request: NextRequest) {
  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const secret = process.env.AUTH_SECRET || process.env.NEXTAUTH_SECRET || ''
  if (!secret) {
    return NextResponse.json({ error: 'Server misconfiguration' }, { status: 500 })
  }

  const token = signHmacJwt(
    {
      sub: session.user.id,
      name: session.user.name,
      email: session.user.email,
      role: session.user.role,
      purpose: 'socket-auth'
    },
    secret,
    60 * 5 // 5 minutes
  )

  return NextResponse.json({ token })
}


